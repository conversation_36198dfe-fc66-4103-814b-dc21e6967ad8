import { pgTable, text, timestamp, varchar } from "drizzle-orm/pg-core";

const commonColumns = {
  id: varchar("id", { length: 255 })
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at")
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
  deletedAt: timestamp("deleted_at").nullable(),
};

/**
 * User table for authentication (Better Auth)
 * @see https://github.com/better-auth/better-auth/blob/main/packages/cli/test/__snapshots__/auth-schema.txt
 */
export const users = pgTable("users", {
  ...commonColumns,
  name: varchar("name", { length: 255 }).notNull(),
  email: varchar("email", { length: 255 }).notNull().unique(),
  emailVerified: boolean("email_verified").default(false).notNull(),
  image: varchar("image", { length: 255 }),
  twoFactorEnabled: boolean("two_factor_enabled"),
  username: varchar("username", { length: 255 }).unique(),
  displayUsername: varchar("display_username", { length: 255 }),
});

/**
 * Session table for authentication (Better Auth)
 */
export const sessions = pgTable("sessions", {
  ...commonColumns,
  expiresAt: timestamp("expires_at").notNull(),
  token: varchar("token", { length: 255 }).notNull().unique(),
  ipAddress: varchar("ip_address", { length: 255 }),
  userAgent: varchar("user_agent", { length: 255 }),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
});

/**
 * Account table for OAuth and password providers (Better Auth)
 */
export const accounts = pgTable("accounts", {
  ...commonColumns,
  accountId: varchar("account_id", { length: 255 }).notNull(),
  providerId: varchar("provider_id", { length: 255 }).notNull(),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  accessToken: varchar("access_token", { length: 255 }),
  refreshToken: varchar("refresh_token", { length: 255 }),
  idToken: varchar("id_token", { length: 255 }),
  accessTokenExpiresAt: timestamp("access_token_expires_at"),
  refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
  scope: varchar("scope", { length: 255 }),
  password: varchar("password", { length: 255 }),
});

/**
 * Verification table for email/phone verification (Better Auth)
 */
export const verifications = pgTable("verifications", {
  ...commonColumns,
  identifier: varchar("identifier", { length: 255 }).notNull(),
  value: varchar("value", { length: 255 }).notNull(),
  expiresAt: timestamp("expires_at").notNull(),
});

/**
 * Two-factor authentication table (Better Auth)
 */
export const twoFactors = pgTable("two_factors", {
  ...commonColumns,
  secret: varchar("secret", { length: 255 }).notNull(),
  backupCodes: varchar("backup_codes", { length: 255 }).notNull(),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
});

const authSchema = {
  users,
  sessions,
  accounts,
  verifications,
  twoFactors,
};

export { authSchema };

export const teams = pgTable("teams", {
  ...commonColumns,
  name: varchar("name", { length: 255 }).notNull(),
  ownerId: varchar("owner_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  description: text("description"),
  avatar: varchar("avatar", { length: 255 }),
});

export const teamMembers = pgTable("team_members", {
  ...commonColumns,
  teamId: varchar("team_id")
    .notNull()
    .references(() => teams.id, { onDelete: "cascade" }),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  role: varchar("role", { length: 255 }).notNull(),
});

export const teamInvitations = pgTable("team_invitations", {
  ...commonColumns,
  teamId: varchar("team_id")
    .notNull()
    .references(() => teams.id, { onDelete: "cascade" }),
  email: varchar("email", { length: 255 }).notNull(),
  invitedById: varchar("invited_by_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  expiresAt: timestamp("expires_at").notNull(),
  acceptedAt: timestamp("accepted_at").nullable(),
  rejectedAt: timestamp("rejected_at").nullable(),
  invoiteToken: varchar("invite_token", { length: 255 }).notNull().unique(),
});

export const teamMemberPermissions = pgTable("team_member_permissions", {
  ...commonColumns,
  teamMemberId: varchar("team_member_id")
    .notNull()
    .references(() => teamMembers.id, { onDelete: "cascade" }),
  permission: varchar("permission", { length: 255 }).notNull(),
});

export const organizations = pgTable("organizations", {
  ...commonColumns,
  name: varchar("name", { length: 255 }).notNull(),
  ownerId: varchar("owner_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  description: text("description"),
  avatar: varchar("avatar", { length: 255 }),
});

export const organizationMembers = pgTable("organization_members", {
  ...commonColumns,
  organizationId: varchar("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  role: varchar("role", { length: 255 }).notNull(),
});

export const organizationInvitations = pgTable("organization_invitations", {
  ...commonColumns,
  organizationId: varchar("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
  email: varchar("email", { length: 255 }).notNull(),
  invitedById: varchar("invited_by_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  expiresAt: timestamp("expires_at").notNull(),
  acceptedAt: timestamp("accepted_at").nullable(),
  rejectedAt: timestamp("rejected_at").nullable(),
  invoiteToken: varchar("invite_token", { length: 255 }).notNull().unique(),
});

export const organizationMemberPermissions = pgTable(
  "organization_member_permissions",
  {
    ...commonColumns,
    organizationMemberId: varchar("organization_member_id")
      .notNull()
      .references(() => organizationMembers.id, { onDelete: "cascade" }),
    permission: varchar("permission", { length: 255 }).notNull(),
  }
);

export const clients = pgTable("clients", {
  ...commonColumns,
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  avatar: varchar("avatar", { length: 255 }),
  ownerId: varchar("owner_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  organizationId: varchar("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
});

export const clientBillingAddress = pgTable("client_billing_address", {
  ...commonColumns,
  clientId: varchar("client_id")
    .notNull()
    .references(() => clients.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 255 }).notNull(),
  line1: varchar("line1", { length: 255 }).notNull(),
  line2: varchar("line2", { length: 255 }),
  city: varchar("city", { length: 255 }).notNull(),
  state: varchar("state", { length: 255 }).notNull(),
  zip: varchar("zip", { length: 255 }).notNull(),
  country: varchar("country", { length: 255 }).notNull(),
});

export const projects = pgTable("projects", {
  ...commonColumns,
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  ownerId: varchar("owner_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  organizationId: varchar("organization_id")
    .notNull()
    .references(() => organizations.id, { onDelete: "cascade" }),
  clientId: varchar("client_id")
    .notNull()
    .references(() => clients.id, { onDelete: "cascade" }),
});

export const projectTasks = pgTable("project_tasks", {
  ...commonColumns,
  projectId: varchar("project_id")
    .notNull()
    .references(() => projects.id, { onDelete: "cascade" }),
  parentTaskId: varchar("parent_task_id").references(() => projectTasks.id, {
    onDelete: "cascade",
  }),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  dueDate: timestamp("due_date").notNull(),
  completedAt: timestamp("completed_at").nullable(),
});

export const projectTaskAssignees = pgTable("project_task_assignees", {
  ...commonColumns,
  taskId: varchar("task_id")
    .notNull()
    .references(() => projectTasks.id, { onDelete: "cascade" }),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
});

export const projectTaskComments = pgTable("project_task_comments", {
  ...commonColumns,
  taskId: varchar("task_id")
    .notNull()
    .references(() => projectTasks.id, { onDelete: "cascade" }),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  parentId: varchar("parent_id").references(() => projectTaskComments.id, {
    onDelete: "cascade",
  }),
  comment: text("comment").notNull(),
});

export const projectTaskAttachments = pgTable("project_task_attachments", {
  ...commonColumns,
  taskId: varchar("task_id")
    .notNull()
    .references(() => projectTasks.id, { onDelete: "cascade" }),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 255 }).notNull(),
  url: varchar("url", { length: 255 }).notNull(),
});

export const invoices = pgTable("invoices", {
  ...commonColumns,
  clientId: varchar("client_id")
    .notNull()
    .references(() => clients.id, { onDelete: "cascade" }),
  number: varchar("number", { length: 255 }).notNull(),
  date: timestamp("date").notNull(),
  dueDate: timestamp("due_date").notNull(),
  amount: numeric("amount").notNull(),
  status: varchar("status", { length: 255 }).notNull(),
});

export const invoiceItems = pgTable("invoice_items", {
  ...commonColumns,
  invoiceId: varchar("invoice_id")
    .notNull()
    .references(() => invoices.id, { onDelete: "cascade" }),
  description: text("description").notNull(),
  quantity: numeric("quantity").notNull(),
  unitPrice: numeric("unit_price").notNull(),
  amount: numeric("amount").notNull(),
});
